# Configuration for OptiFine's Natural Textures feature
# Location: /assets/minecraft/optifine/natural.properties
#
# Configuration format
# <texture_name> = <value>
#
# Values
# 4 = Rotate x 90° (4 variants)
# 2 = Rotate x 180° (2 variants)
# F = Flip texture horizontally (2 variants)
# 4F = 4 + Flip (8 variants)
# 2F = 2 + Flip (4 variants)
#
# Example for obsidian texture which can rotate with 4 variants and flip
# obsidian = 4F
#

# Grass
grass_side = F
grass_side_overlay = F
grass_side_snowed = F
mycelium_side = F
mycelium_top = 4F
# Snow
snow = 4F
# Dirt
coarse_dirt = 4F
farmland_wet = 2F
farmland_dry = 2F
# Stone
sandstone_top = 4
sandstone_bottom = 4F
stone_slab_top = F
end_stone = 4
# Gravel
gravel = 2
clay = 4F
# Logs
log_oak = 2F
log_spruce = 2F
log_birch = F
log_jungle = 2F
log_acacia = 2F
log_big_oak = 2F
log_oak_top = 4F
log_spruce_top = 4F
log_birch_top = 4F
log_jungle_top = 4F
log_acacia_top = 4F
log_big_oak_top = 4F
# Leaves
leaves_oak = 2F
leaves_spruce = 2F
leaves_birch = 2F
leaves_jungle = 2
leaves_big_oak = 2F
leaves_acacia = 2F
# Ores
gold_ore = 2F
iron_ore = 2F
coal_ore = 2F
diamond_ore = 2F
redstone_ore = 2F
lapis_ore = 2F
# Nether
netherrack = 4F
quartz_ore = 2
soul_sand = 4F
glowstone = 4
# Redstone
redstone_lamp_on = 4F
redstone_lamp_off = 4F
# Prismarine
# Misc
obsidian = 4F
cactus_side = 2F