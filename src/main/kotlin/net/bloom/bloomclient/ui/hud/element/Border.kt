package net.bloom.bloomclient.ui.hud.element

import net.bloom.bloomclient.utils.render.RenderUtils
import java.awt.Color

data class Border(val x: Float, val y: Float, val x2: Float, val y2: Float) {

    constructor(x: Int, y: Int, x2: Int, y2: Int): this(x.toFloat(), y.toFloat(), x2.toFloat(), y2.toFloat())
    constructor(x: Double, y: Double, x2: Double, y2: Double): this(x.toFloat(), y.toFloat(), x2.toFloat(), y2.toFloat())

    fun draw() {
        RenderUtils.drawRect(x.toInt(), y.toInt(), x2.toInt(), y2.toInt(), Color(255, 0, 0, 100))
    }

}