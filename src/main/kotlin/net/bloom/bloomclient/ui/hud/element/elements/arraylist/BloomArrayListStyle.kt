package net.bloom.bloomclient.ui.hud.element.elements.arraylist

import net.bloom.bloomclient.BloomClient
import net.bloom.bloomclient.ui.hud.element.Border
import net.minecraft.client.gui.Side
import java.awt.Color
import kotlin.math.max
import net.bloom.bloomclient.ui.hud.element.elements.ArrayList
import net.bloom.bloomclient.utils.render.skiko.Skiko
import net.bloom.bloomclient.utils.render.skiko.SkikoContext
import net.bloom.bloomclient.utils.render.skiko.font.Fonts
import org.jetbrains.skia.ImageFilter
import org.jetbrains.skia.Paint
import org.jetbrains.skia.Path
import org.jetbrains.skia.PathEffect
import org.jetbrains.skia.PathOp
import org.jetbrains.skia.Rect

class BloomArrayListStyle(parent: ArrayList): ArrayListStyle(parent, "Bloom") {
    private val nunito = Fonts.NUNITO[10F]
    private val metrics = nunito.metrics

    private val backgroundColor = Color(32, 34, 37, 220)
    private val textColor = Color(255, 255, 255)

    override fun drawElement(): Border {
        val sortedModules = BloomClient.moduleManager.modules
            .filter { it.state || it.alphaAnimation.current > 0.01f } // Show if enabled or still fading
            .sortedByDescending { nunito.width(it.displayName) }

        if (sortedModules.isEmpty()) return Border(0f, 0f, 0f, 0f)

        updateAnimations()

        var yPos = parent.renderY
        var maxWidth = 0f
        var maxHeight = 0f

        val padding = 3F
        val lineSpacing = 0F
        val lineHeight = metrics.descent - metrics.ascent

        SkikoContext.preFlush()

        var bgPath = Path()
        bgPath.use {
            var tempY = yPos

            for (module in sortedModules) {
                val animationValue = module.alphaAnimation.current
                val textWidth = nunito.width(module.displayName)
                val textHeight = lineHeight + padding * 2
                val animatedHeight = lineHeight * animationValue

                // Calculate position based on side settings and animation
                val xOffset = when (parent.side.horizontal) {
                    Side.Horizontal.LEFT -> 0f
                    Side.Horizontal.MIDDLE -> -textWidth / 2
                    Side.Horizontal.RIGHT -> -textWidth
                }

                val xPos = parent.renderX + xOffset

                // Apply slide animation
                val animatedXPos = when (parent.side.horizontal) {
                    Side.Horizontal.LEFT -> xPos - (1f - module.alphaAnimation.current) * textWidth
                    Side.Horizontal.MIDDLE -> xPos
                    Side.Horizontal.RIGHT -> xPos + (1f - module.alphaAnimation.current) * textWidth
                }

                val bounds = Rect.makeLTRB(
                    animatedXPos - padding,
                    tempY,
                    animatedXPos + textWidth + padding,
                    tempY + animatedHeight,
                )

                bgPath = Path.makeCombining(bgPath, Path().addRect(bounds), PathOp.UNION) ?: Path()
                tempY += animatedHeight

                maxWidth = max(maxWidth, textWidth)
                maxHeight += textHeight * module.alphaAnimation.current
            }

            val overallAlpha = if (sortedModules.isNotEmpty()) {
                sortedModules.maxOf { it.alphaAnimation.current }
            } else 1F

            val animatedBgPaint = Paint().apply {
                color = Color(0, 0, 0, (150 * overallAlpha).toInt()).rgb
                imageFilter = ImageFilter.makeDropShadow(0F, 0F, 10F, 10F, color)
                pathEffect = PathEffect.makeCorner(5F)
            }

            Skiko.drawPath(bgPath, animatedBgPaint)

            var textY = yPos + metrics.height / 2

            for (module in sortedModules) {
                val animationValue = module.alphaAnimation.current
                val textWidth = nunito.width(module.displayName)

                // Calculate position based on side settings and animation
                val xOffset = when (parent.side.horizontal) {
                    Side.Horizontal.LEFT -> 0f
                    Side.Horizontal.MIDDLE -> -textWidth / 2
                    Side.Horizontal.RIGHT -> -textWidth
                }

                val xPos = parent.renderX + xOffset

                // Apply slide animation
                val animatedXPos = when (parent.side.horizontal) {
                    Side.Horizontal.LEFT -> xPos - (1f - module.alphaAnimation.current) * textWidth
                    Side.Horizontal.MIDDLE -> xPos
                    Side.Horizontal.RIGHT -> xPos + (1f - module.alphaAnimation.current) * textWidth
                }

                val animatedTextPaint = Paint().apply {
                    color = Color(
                        (255 * animationValue).toInt(),
                        (255 * animationValue).toInt(),
                        (255 * animationValue).toInt(),
                        (255 * animationValue).toInt()
                    ).rgb
                }

                nunito.text(
                    module.displayName,
                    animatedXPos,
                    textY + metrics.descent,
                    animatedTextPaint
                )

                textY += (lineHeight * animationValue) + lineSpacing
            }
        }

        /*
        // Draw each module with animations
        for ((index, module) in sortedModules.withIndex()) {
            if (module.alphaAnimation.current <= 0.01f) continue

            val textWidth = nunito.width(module.displayName)
            val rectWidth = textWidth + hoverPadding * 2
            val rectHeight = nunito.height(module.displayName) + hoverPadding

            // Calculate position based on side settings and animation
            val xOffset = when (parent.side.horizontal) {
                Side.Horizontal.LEFT -> 0f
                Side.Horizontal.MIDDLE -> -rectWidth / 2
                Side.Horizontal.RIGHT -> -rectWidth
            }

            val xPos = parent.renderX.toFloat() + xOffset

            // Apply slide animation
            val animatedXPos = when (parent.side.horizontal) {
                Side.Horizontal.LEFT -> xPos - (1f - module.alphaAnimation.current) * rectWidth
                Side.Horizontal.MIDDLE -> xPos
                Side.Horizontal.RIGHT -> xPos + (1f - module.alphaAnimation.current) * rectWidth
            }


            // Calculate text position
            val textX = when (parent.side.horizontal) {
                Side.Horizontal.MIDDLE -> animatedXPos + (rectWidth - textWidth)
                else -> animatedXPos + hoverPadding
            }
            val textY = yPos + hoverPadding / 2

            // Draw text with animation
            val textAlpha = (255 * module.alphaAnimation.current).toInt()
            val moduleTextColor = Color(
                textColor.red,
                textColor.green,
                textColor.blue,
                textAlpha
            )

            lexendFont.drawString(
                module.displayName,
                textX.toInt(),
                textY.toInt(),
                moduleTextColor.rgb
            )

            yPos += rectHeight * module.alphaAnimation.current
            maxWidth = max(maxWidth, rectWidth)
            maxHeight += rectHeight * module.alphaAnimation.current
        }*/

        SkikoContext.flush()

        // Calculate border based on alignment
        return when (parent.side.horizontal) {
            Side.Horizontal.LEFT -> Border(parent.renderX, parent.renderY, parent.renderX + maxWidth, parent.renderY + maxHeight)
            Side.Horizontal.MIDDLE -> Border(parent.renderX - maxWidth / 2, parent.renderY, parent.renderX + maxWidth / 2, parent.renderY + maxHeight)
            Side.Horizontal.RIGHT -> Border(parent.renderX - maxWidth, parent.renderY, parent.renderX, parent.renderY + maxHeight)
        }
    }

    /**
     * Update animation values for each module
     */
    private fun updateAnimations() {
        BloomClient.moduleManager.modules.forEach {
            it.alphaAnimation.target = if (it.state) 1f else 0f
            it.alphaAnimation.update(0.15f )
        }
    }
}