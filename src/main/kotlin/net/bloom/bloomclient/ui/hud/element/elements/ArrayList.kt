package net.bloom.bloomclient.ui.hud.element.elements

import net.bloom.bloomclient.ui.hud.element.Border
import net.bloom.bloomclient.ui.hud.element.Element
import net.bloom.bloomclient.ui.hud.element.ElementInfo
import net.bloom.bloomclient.ui.hud.element.elements.arraylist.BloomArrayListStyle
import net.minecraft.client.gui.Side

@ElementInfo(name = "ArrayList", single = true)
class ArrayList(
    x: Float = 6F,
    y: Float = 2F,
    scale: Float = 1F,
    side: Side = Side(Side.Horizontal.RIGHT, Side.Vertical.TOP)
): Element("Arraylist", x, y, scale, side) {
    private val style by mode("Style", arrayOf(
        BloomArrayListStyle(this)
    ))

    override fun drawElement(): Border = style.drawElement()

    override val state = false // no use event handler
}