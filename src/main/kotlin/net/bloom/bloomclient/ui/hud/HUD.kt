package net.bloom.bloomclient.ui.hud

import net.bloom.bloomclient.ui.hud.designer.GuiHUDDesigner
import net.bloom.bloomclient.ui.hud.element.Element
import net.bloom.bloomclient.ui.hud.element.ElementInfo
import net.bloom.bloomclient.ui.hud.element.elements.*
import net.bloom.bloomclient.ui.hud.element.elements.ArrayList
import net.bloom.bloomclient.ui.hud.element.elements.DymanicIsland
import net.bloom.bloomclient.utils.ClientUtils
import net.minecraft.client.MinecraftInstance
import net.minecraft.client.gui.ScaledResolution
import org.lwjgl.opengl.GL11
import java.util.*

import kotlin.math.*

object HUD: MinecraftInstance() {
    val elements = mutableListOf<Element>()

    private val ALL_ELEMENT_CLASSES = arrayOf(
        Armor::class.java,
        ArrayList::class.java,
        DymanicIsland::class.java,
        TestElement::class.java
    )

    val ELEMENTS = ALL_ELEMENT_CLASSES.associateWithTo(IdentityHashMap(ALL_ELEMENT_CLASSES.size)) {
        it.getAnnotation(ElementInfo::class.java)
    }

    /** Render all elements */
    fun render(designer: Boolean) {
        elements.forEach {
            GL11.glPushMatrix()

            if (!it.info.disableScale)
                GL11.glScalef(it.scale, it.scale, it.scale)

            GL11.glTranslatef(it.renderX, it.renderY, 0f)

            try {
                it.border = it.drawElement()

                if (designer) it.border?.draw()
            } catch (ex: Exception) {
                ClientUtils.LOGGER.error("Something went wrong while drawing ${it.name} element in HUD.", ex)
            }

            GL11.glPopMatrix()
        }
    }

    fun setDefault() {
        elements.clear()
        elements.add(Armor())
        elements.add(ArrayList())
//        elements.add(DymanicIsland())
    }

    /** Update all elements */
    fun update() {
        for (element in elements) element.updateElement()
    }

    /** Handle mouse click */
    fun handleMouseClick(mouseX: Int, mouseY: Int, button: Int) {
        for (element in elements) {
            val x = mouseX / element.scale - element.renderX
            val y = mouseY / element.scale - element.renderY
            element.handleMouseClick(x, y, button)
        }

        if (button == 0) {
            for (element in elements.reversed()) {
                val x = mouseX / element.scale - element.renderX
                val y = mouseY / element.scale - element.renderY
                if (!element.isInBorder(x, y))
                    continue

                element.drag = true
                elements -= element
                elements += element
                break
            }
        }
    }

    /** Handle released mouse key */
    fun handleMouseReleased() {
        for (element in elements)
            element.drag = false
    }

    /** Handle mouse move */
    fun handleMouseMove(mouseX: Int, mouseY: Int) {
        if (mc.currentScreen !is GuiHUDDesigner)
            return

        val sc = ScaledResolution(mc)

        for (element in elements) {
            val scaledX = mouseX / element.scale
            val scaledY = mouseY / element.scale
            val prevMouseX = element.prevMouseX
            val prevMouseY = element.prevMouseY

            element.prevMouseX = scaledX
            element.prevMouseY = scaledY

            if (element.drag) {
                val moveX = scaledX - prevMouseX
                val moveY = scaledY - prevMouseY

                if (moveX == 0F && moveY == 0F)
                    continue

                val border = element.border ?: continue

                val minX = min(border.x, border.x2) + 1
                val minY = min(border.y, border.y2) + 1

                val maxX = max(border.x, border.x2) - 1
                val maxY = max(border.y, border.y2) - 1

                val width = sc.scaledWidth / element.scale
                val height = sc.scaledHeight / element.scale

                if ((element.renderX + minX + moveX >= 0.0 || moveX > 0) && (element.renderX + maxX + moveX <= width || moveX < 0))
                    element.renderX = moveX

                if ((element.renderY + minY + moveY >= 0.0 || moveY > 0) && (element.renderY + maxY + moveY <= height || moveY < 0))
                    element.renderY = moveY
            }
        }
    }

    /** Handle incoming key */
    fun handleKey(c: Char, keyCode: Int) {
        for (element in elements)
            element.handleKey(c, keyCode)
    }

    /** Add [element] to HUD */
    fun addElement(element: Element): HUD {
        elements += element

        elements.sortBy { -it.info.priority }

        element.updateElement()
        return this
    }

    /** Remove [element] from HUD */
    fun removeElement(element: Element): HUD {
        element.destroyElement()
        elements.remove(element)
        return this
    }

    /** Clear all elements */
    fun clearElements() {
        for (element in elements)
            element.destroyElement()

        elements.clear()
    }
}