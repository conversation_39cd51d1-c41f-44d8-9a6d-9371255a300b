package net.bloom.bloomclient.utils.render.skiko

import net.bloom.bloomclient.utils.render.skiko.SkikoContext.canvas
import org.jetbrains.skia.*
import java.awt.Color

/**
 * Skiko Rendering Utility
 * <AUTHOR>
 */
object Skiko {
    fun save() {
        canvas.save()
    }

    fun restore() {
        canvas.restore()
    }

    fun scale(scale: Float) {
        canvas.scale(scale, scale)
    }

    fun translate(x: Float, y: Float) {
        canvas.translate(x, y)
    }

    fun alpha(alpha: Float) {
        canvas.saveLayer(null, Paint().setAlphaf(alpha))
    }

    fun paint(color: Color) = Paint().setARGB(
        color.alpha,
        color.red,
        color.green,
        color.blue
    )

    fun drawPath(path: Path, paint: Paint) {
        canvas.drawPath(path, paint)
    }

    fun clip(x: Float, y: Float, width: Float, height: Float, radius: Float) {
        val path = Path()
        path.addRRect(RRect.makeXYWH(x, y, width, height, radius))
        clipPath(path)
    }

    fun clipPath(path: Path) {
        canvas.clipPath(path, ClipMode.INTERSECT, true)
    }

    fun shadow(blur: Float, color: Color) {
        val paint = Paint().setARGB(color.alpha, color.red, color.green, color.blue)
        paint.imageFilter = ImageFilter.makeDropShadow(blur, blur, 0F, 0F, paint.color)
        canvas.saveLayer(null, paint)
    }

    fun rect(x: Float, y: Float, width: Float, height: Float, color: Color) {
        canvas.drawRect(Rect.makeXYWH(x, y, width, height), paint(color))
    }

    fun roundrect(x: Float, y: Float, width: Float, height: Float, radius: Float, color: Color) {
        canvas.drawRRect(RRect.makeXYWH(x, y, width, height, radius), paint(color))
    }

    fun roundrect(x: Float, y: Float, width: Float, height: Float, radius: Float, paint: Paint) {
        canvas.drawRRect(RRect.makeXYWH(x, y, width, height, radius), paint)
    }

    fun image(x: Float, y: Float, image: Image) {
        canvas.drawImage(image, x, y)
    }

    fun mouseOver(x: Float, y: Float, width: Float, height: Float, mouseX: Float, mouseY: Float) =
        mouseX in x..(x + width) && mouseY in y..(y + height)
}