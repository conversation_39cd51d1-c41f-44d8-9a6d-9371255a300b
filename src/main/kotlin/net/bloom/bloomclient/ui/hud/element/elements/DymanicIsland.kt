package net.bloom.bloomclient.ui.hud.element.elements

import net.bloom.bloomclient.utils.animations.Animation
import net.bloom.bloomclient.BloomClient
import net.bloom.bloomclient.font.Fonts
import net.bloom.bloomclient.ui.hud.element.Border
import net.bloom.bloomclient.ui.hud.element.Element
import net.bloom.bloomclient.ui.hud.element.ElementInfo
import net.minecraft.client.gui.Side
import net.bloom.bloomclient.utils.animations.Easing
import net.bloom.bloomclient.utils.render.NanoVGUtils
import net.minecraft.client.Minecraft
import net.minecraft.client.gui.ScaledResolution
import java.awt.Color
import kotlin.math.sqrt

@ElementInfo(name = "DymanicIsland", single = true)
class DymanicIsland(
    x: Float = 0F,
    y: Float = 4F,
    scale: Float = 1F,
    side: Side = Side(Side.Horizontal.MIDDLE, Side.Vertical.TOP)
) : Element("DynamicIsland", x, y, scale, side) {

    private val animState = Animation(Easing.EASE_OUT_QUART, 400L)
    private val animWidth = Animation(Easing.EASE_OUT_ELASTIC, 400L)
    private val animHeight = Animation(Easing.EASE_OUT_ELASTIC, 400L)
    private val animAlpha = Animation(Easing.EASE_OUT_CUBIC, 300L)
    private val pulseAnim = Animation(Easing.EASE_IN_OUT_SINE, 1500L)
    private val progressAnim = Animation(Easing.EASE_OUT_CUBIC, 500L)
    private val moduleToggleAnim = Animation(Easing.EASE_OUT_CUBIC, 300L)
    private val blocksPerSecondAnim = Animation(Easing.EASE_OUT_CUBIC, 300L)

    enum class IslandState(val widthMultiplier: Float, val heightMultiplier: Float) {
        WIDE(1.6f, 1.0f),        // For main client info (default)
        LARGE(2.5f, 1.5f),       // For module info (e.g., Scaffold)
        NOTIFICATION(1.85f, 1.2f) // For notifications
    }

    private var currentState = IslandState.WIDE
    private var targetState = IslandState.WIDE
    private var stateProgress = 0F
    private var lastStateChange = 0L
    private val stateChangeCooldown = 200L

    private val baseWidth = 180f
    private val baseHeight = 30f

    private var displayMode = DisplayMode.CLIENT
    private var lastDisplayMode = DisplayMode.CLIENT
    private var moduleInfo: ModuleInfo? = null
    private var notificationInfo: NotificationInfo? = null

    private var scaffoldActive = false
    private var scaffoldLastUpdate = 0L
    private var initialBlockCount = 0
    private var lastBlockCount = 0
    private var lastBlockCountTime = 0L
    private var blocksPerSecond = 0.0f

    // Updated color scheme to match the image
    private val backgroundColor = Color(23, 22, 26, 250)    // Dark background
    private val accentColor = Color(137, 113, 255)         // Purple accent for bloom logo
    private val moduleAccentColor = Color(145, 144, 58)    // Yellow-green for Scaffold
    private val textColor = Color(255, 255, 255)           // White text
    private val secondaryTextColor = Color(200, 200, 200, 230) // Light gray text
    private val cornerRadius = 16f                         // More rounded corners to match image
    private val shadowRadius = 8f                         // Enhanced shadow
    private val shadowAlpha = 0.6f                         // More visible shadow

    private var moduleToggled = false
    private var moduleToggleTime = 0L
    private val moduleToggleDuration = 2000L

    private var progressValue = 0f
    private var targetProgressValue = 0f

    private val moduleStates = mutableMapOf<String, Boolean>()
    private val moduleLastToggleTime = mutableMapOf<String, Long>()
    private val excludedFromNotification = setOf("Scaffold")

    data class ModuleInfo(
        val name: String,
        val displayName: String,
        val infoText: String,
        val color: Color,
        val showProgress: Boolean = false,
        val progressValue: Float = 0f,
        val startTime: Long = System.currentTimeMillis(),
        val isEnabled: Boolean = true
    )

    data class NotificationInfo(
        val title: String,
        val message: String,
        val icon: NotificationIcon = NotificationIcon.INFO,
        val duration: Long = 3000L,
        val showProgress: Boolean = false,
        val progressValue: Float = 0f,
        val startTime: Long = System.currentTimeMillis()
    )

    enum class DisplayMode {
        CLIENT,
        MODULE,
        NOTIFICATION,
        MODULE_TOGGLE
    }

    enum class NotificationIcon {
        INFO, WARNING, SUCCESS, ERROR
    }

    override fun drawElement(): Border {
        val sr = ScaledResolution(mc)
        val screenWidth = sr.scaledWidth
        val screenHeight = sr.scaledHeight

        updateState()

        stateProgress = animState.value
        animWidth.run((baseWidth * getCurrentStateWidthMultiplier()))
        animHeight.run((baseHeight * getCurrentStateHeightMultiplier()))
        animAlpha.run(1F)
        pulseAnim.run(if (pulseAnim.value >= 1F) 0F else 1F)

        if (shouldShowProgress()) {
            progressAnim.run(getProgressValue())
        }

        if (moduleToggled && System.currentTimeMillis() - moduleToggleTime < moduleToggleDuration) {
            moduleToggleAnim.run(1F)
        } else {
            moduleToggleAnim.run(0F)
        }

        val width = animWidth.value.toFloat()
        val height = animHeight.value.toFloat()

        val xPos = when (side.horizontal) {
            Side.Horizontal.LEFT -> renderX.toFloat()
            Side.Horizontal.MIDDLE -> screenWidth / 2f - width / 2f
            Side.Horizontal.RIGHT -> screenWidth.toFloat() - renderX.toFloat() - width
        }

        val yPos = when (side.vertical) {
            Side.Vertical.TOP -> renderY.toFloat() + 10f
            Side.Vertical.MIDDLE -> screenHeight / 2f - height / 2f
            Side.Vertical.BOTTOM -> screenHeight.toFloat() - renderY.toFloat() - height
        }

        NanoVGUtils.preRender()
        NanoVGUtils.save()

        NanoVGUtils.drawShadowRoundedRect(
            xPos, yPos, width, height,
            cornerRadius, shadowRadius,
            shadowAlpha, backgroundColor
        )

        NanoVGUtils.drawRect(
            xPos, yPos, width, height,
            cornerRadius, backgroundColor
        )

        when (displayMode) {
            DisplayMode.CLIENT -> drawClientInfo(xPos, yPos, width, height)
            DisplayMode.MODULE -> drawModuleInfo(xPos, yPos, width, height)
            DisplayMode.NOTIFICATION -> drawNotification(xPos, yPos, width, height)
            DisplayMode.MODULE_TOGGLE -> drawModuleToggle(xPos, yPos, width, height)
        }

        NanoVGUtils.restore()
        NanoVGUtils.postRender()

        return Border(xPos, yPos, xPos + width, yPos + height)
    }

    private fun drawClientInfo(xPos: Float, yPos: Float, width: Float, height: Float) {
        val logoRadius = 12f
        val logoCenterY = yPos + height / 2f


        val clientName = "bloom"
        val versionName = BloomClient.CLIENT_VERSION
        val userName = mc.session.gameProfile.name
        val fpsInfo = "${Minecraft.getDebugFPS()} fps"
        val serverMS = "${mc.thePlayer.ping} ms"
        val icon30Font = Fonts.fontIconGUI[30]
        val fontMain = Fonts.fontLexend[14]
        val fontVersion = Fonts.fontLexend[7]
        val fontInfo = Fonts.fontLexend[10]
        icon30Font.drawString("Q", xPos + logoRadius - 5f, logoCenterY - 15f, accentColor.rgb)
        val userNameWidth = fontInfo.getStringWidth(userName)
        val fpsWidth = fontInfo.getStringWidth(fpsInfo)

        val mainTextY = logoCenterY - 10f
        val versionTextY = logoCenterY + 5f

        fontMain.drawString(
            clientName,
            xPos + logoRadius * 2 + 14f,
            mainTextY,
            textColor.rgb
        )

        fontVersion.drawString(
            "v$versionName",
            xPos + logoRadius * 2 + 14f,
            versionTextY,
            secondaryTextColor.rgb
        )

        val infoStartX = xPos + logoRadius * 2 + 14f + 50f

        val spacing = 4f
        val textPadding = 8f

        NanoVGUtils.drawRect(infoStartX, mainTextY + 6f, 3f, 10f, 2f, textColor)
        fontInfo.drawString(
            userName,
            infoStartX + textPadding,
            mainTextY + 6f,
            textColor.rgb
        )

        val fpsX = infoStartX + userNameWidth + textPadding + spacing
        NanoVGUtils.drawRect(fpsX, mainTextY + 6f, 3f, 10f, 2f, textColor)
        fontInfo.drawString(
            fpsInfo,
            fpsX + textPadding,
            mainTextY + 6f,
            textColor.rgb
        )

        // Ping section with separator - using equal spacing
        val pingX = fpsX + fpsWidth + textPadding + spacing
        NanoVGUtils.drawRect(pingX, mainTextY + 6f, 3f, 10f, 2f, textColor)
        fontInfo.drawString(
            serverMS,
            pingX + textPadding,
            mainTextY + 6f,
            textColor.rgb
        )
    }

    private fun drawModuleInfo(xPos: Float, yPos: Float, width: Float, height: Float) {
        val module = moduleInfo ?: return

        if (module.name == "Scaffold") {
            // Left side: block icon
            val iconSize = 30f
            val iconX = xPos + 15f
            val iconY = yPos + height / 2f - iconSize / 2f

            NanoVGUtils.drawRect(
                iconX,
                iconY,
                iconSize,
                iconSize,
                8f,
                moduleAccentColor
            )

            drawBlockIcon(iconX + iconSize/2, iconY + iconSize/2, 8f)

            val fontMain = Fonts.fontLexend[14]
            val fontBps = Fonts.fontLexend[9]
            val fontBlocks = Fonts.fontLexend[14]
            val fontBlocksLabel = Fonts.fontLexend[14]

            // Module name & status aligned to match image
            fontMain.drawString(
                "Scaffold",
                xPos + 55f,
                yPos + height / 2f - 12f,
                moduleAccentColor.rgb
            )

            fontMain.drawString(
                "Toggled",
                xPos + 117f,
                yPos + height / 2f - 12f,
                textColor.rgb
            )

            // Progress bar - updated to match image style
            if (shouldShowProgress()) {
                // Background of progress bar
                NanoVGUtils.drawRect(
                    xPos + 55f,
                    yPos + height / 2f + 5f,
                    width - 140f,
                    6f,
                    3f,
                    Color(50, 50, 50, 150)
                )
                // Foreground of progress bar with module color
                NanoVGUtils.drawRect(
                    xPos + 55f,
                    yPos + height / 2f + 5f,
                    (width - 140f) * progressAnim.value.toFloat(),
                    6f,
                    3f,
                    moduleAccentColor
                )
            }

            // Block count - prominent display matching image
            val blockCount = getBlockCount().toString()
            val blockCountStringWidth = fontBlocks.getStringWidth(blockCount)
            fontBlocks.drawString(
                blockCount,
                xPos + width - 75f,
                yPos + height / 2f - 12f,
                moduleAccentColor.rgb
            )
            fontBlocksLabel.drawString(
                "blocks",
                xPos + width + blockCountStringWidth - 72f,
                yPos + height / 2f - 12f,
                textColor.rgb
            )

            // Blocks per second
            blocksPerSecondAnim.run(blocksPerSecond)
            val bpsText = String.format("%.2f b/s", blocksPerSecondAnim.value.toFloat())
            fontBps.drawString(
                bpsText,
                xPos + width - 75f,
                yPos + height - 18f,
                secondaryTextColor.rgb
            )
            return
        }

        // Other modules info display
        val fontMain = Fonts.fontLexend[10]
        fontMain.drawString(
            module.displayName,
            xPos + 18f,
            yPos + 12f,
            module.color.rgb
        )

        fontMain.drawString(
            module.infoText,
            xPos + 18f,
            yPos + 12f + fontMain.height + 2f,
            secondaryTextColor.rgb
        )

        if (module.showProgress) {
            targetProgressValue = module.progressValue
        }
    }

    // Helper function to draw block icon
    private fun drawBlockIcon(centerX: Float, centerY: Float, size: Float) {
        val halfSize = size / 2
        val color = Color(255, 255, 255, 200)

        // Draw cube outline
//        NanoVGUtils.drawLine(centerX - halfSize, centerY - halfSize, centerX + halfSize, centerY - halfSize, 1f, color)
//        NanoVGUtils.drawLine(centerX + halfSize, centerY - halfSize, centerX + halfSize, centerY + halfSize, 1f, color)
//        NanoVGUtils.drawLine(centerX + halfSize, centerY + halfSize, centerX - halfSize, centerY + halfSize, 1f, color)
//        NanoVGUtils.drawLine(centerX - halfSize, centerY + halfSize, centerX - halfSize, centerY - halfSize, 1f, color)
//
//        // Draw 3D effect lines
//        NanoVGUtils.drawLine(centerX - halfSize, centerY - halfSize, centerX - halfSize/2, centerY - halfSize*1.5f, 1f, color)
//        NanoVGUtils.drawLine(centerX + halfSize, centerY - halfSize, centerX + halfSize*1.5f, centerY - halfSize/2, 1f, color)
//        NanoVGUtils.drawLine(centerX + halfSize*1.5f, centerY - halfSize/2, centerX + halfSize*1.5f, centerY + halfSize/2, 1f, color)
//        NanoVGUtils.drawLine(centerX + halfSize*1.5f, centerY + halfSize/2, centerX + halfSize, centerY + halfSize, 1f, color)
//        NanoVGUtils.drawLine(centerX - halfSize/2, centerY - halfSize*1.5f, centerX + halfSize, centerY - halfSize, 1f, color)
    }

    private fun drawNotification(xPos: Float, yPos: Float, width: Float, height: Float) {
        val notification = notificationInfo ?: return

        // Improved notification display
        val timeElapsed = System.currentTimeMillis() - notification.startTime
        val timeRemaining = notification.duration - timeElapsed
        val timeProgress = (timeRemaining.toFloat() / notification.duration.toFloat()).coerceIn(0f, 1f)

        val iconColor = when (notification.icon) {
            NotificationIcon.INFO -> Color(88, 101, 242)
            NotificationIcon.WARNING -> Color(255, 184, 0)
            NotificationIcon.SUCCESS -> Color(0, 184, 148)
            NotificationIcon.ERROR -> Color(255, 82, 82)
        }

        // Draw notification icon
        NanoVGUtils.drawCircle(
            xPos + 24f,
            yPos + height / 2,
            12f,
            iconColor
        )

        // Draw icon symbol based on type
        val fontIcon = Fonts.fontLexend[14]
        val iconSymbol = when (notification.icon) {
            NotificationIcon.INFO -> "i"
            NotificationIcon.WARNING -> "!"
            NotificationIcon.SUCCESS -> "✓"
            NotificationIcon.ERROR -> "✗"
        }

        fontIcon.drawString(
            iconSymbol,
            xPos + 21f,
            yPos + height / 2 - 7f,
            Color.WHITE.rgb
        )

        val fontTitle = Fonts.fontLexend[12]
        val fontMessage = Fonts.fontLexend[10]

        // Draw notification title with more emphasis
        fontTitle.drawString(
            notification.title,
            xPos + 44f,
            yPos + 10f,
            textColor.rgb
        )

        // Draw notification message
        fontMessage.drawString(
            notification.message,
            xPos + 44f,
            yPos + 10f + fontTitle.height + 4f,
            secondaryTextColor.rgb
        )

        // Draw progress bar for notification time remaining
        val progressBarY = yPos + height - 8f

        NanoVGUtils.drawRect(
            xPos + 44f,
            progressBarY,
            width - 60f,
            4f,
            2f,
            Color(60, 60, 60, 150)
        )

        NanoVGUtils.drawRect(
            xPos + 44f,
            progressBarY,
            (width - 60f) * (if (notification.showProgress) notification.progressValue else timeProgress),
            4f,
            2f,
            iconColor
        )

        targetProgressValue = if (notification.showProgress) {
            notification.progressValue
        } else {
            timeProgress
        }
    }

    private fun drawModuleToggle(xPos: Float, yPos: Float, width: Float, height: Float) {
        val module = moduleInfo ?: return

        // Improved module toggle notification
        val statusColor = if (module.isEnabled) Color(0, 184, 148) else Color(255, 82, 82)

        // Draw status icon
        NanoVGUtils.drawCircle(
            xPos + 24f,
            yPos + height / 2,
            12f,
            statusColor
        )

        // Draw check or X symbol
        val fontIcon = Fonts.fontLexend[14]
        val statusSymbol = if (module.isEnabled) "✓" else "✗"

        fontIcon.drawString(
            statusSymbol,
            xPos + 20f,
            yPos + height / 2 - 7f,
            Color.WHITE.rgb
        )

        val fontTitle = Fonts.fontLexend[12]
        val fontStatus = Fonts.fontLexend[10]

        // Module toggle title
        fontTitle.drawString(
            "Module Toggled",
            xPos + 44f,
            yPos + 6f,
            textColor.rgb
        )

        // Module toggle status
        val stateText = "${module.displayName} has been ${if (module.isEnabled) "enabled" else "disabled"}!"
        fontStatus.drawString(
            stateText,
            xPos + 44f,
            yPos + 10f + fontTitle.height,
            secondaryTextColor.rgb
        )

        // Status text on right side
        val enabledText = if (module.isEnabled) "Enabled" else "Disabled"
        val textWidth = fontTitle.getStringWidth(enabledText)

        fontTitle.drawString(
            enabledText,
            xPos + width - 16f - textWidth,
            yPos + height / 2 - 6f,
            statusColor.rgb
        )
    }

    private fun shouldShowProgress(): Boolean {
        return when (displayMode) {
            DisplayMode.NOTIFICATION -> notificationInfo?.showProgress ?: false
            DisplayMode.MODULE -> {
                moduleInfo?.name == "Scaffold" || moduleInfo?.showProgress == true
            }
            else -> false
        }
    }

    private fun updateState() {
        updateModuleTracking()
        updateBlocksPerSecond()

        if (moduleToggled && System.currentTimeMillis() - moduleToggleTime < moduleToggleDuration) {
            if (displayMode != DisplayMode.MODULE_TOGGLE) {
                lastDisplayMode = displayMode
                displayMode = DisplayMode.MODULE_TOGGLE
                targetState = IslandState.NOTIFICATION
                updateStateNow()
            }
        }
        else if (notificationInfo != null &&
            System.currentTimeMillis() - (notificationInfo?.startTime ?: 0) < (notificationInfo?.duration ?: 0)) {
            if (displayMode != DisplayMode.NOTIFICATION) {
                lastDisplayMode = displayMode
                displayMode = DisplayMode.NOTIFICATION
                targetState = IslandState.NOTIFICATION
                updateStateNow()
            }
        }
        else if (scaffoldActive) {
            if (displayMode != DisplayMode.MODULE || moduleInfo?.name != "Scaffold") {
                lastDisplayMode = displayMode
                displayMode = DisplayMode.MODULE

                moduleInfo = ModuleInfo(
                    name = "Scaffold",
                    displayName = "Scaffold",
                    infoText = "${getBlockCount()} blocks left",
                    color = moduleAccentColor,
                    showProgress = true,
                    progressValue = getBlockProgress()
                )

                targetState = IslandState.LARGE
                updateStateNow()
            } else {
                moduleInfo = moduleInfo?.copy(
                    infoText = "${getBlockCount()} blocks left",
                    progressValue = getBlockProgress()
                )
            }
        }
        else {
            notificationInfo = null

            if (displayMode != DisplayMode.CLIENT) {
                lastDisplayMode = displayMode
                displayMode = DisplayMode.CLIENT
                targetState = IslandState.WIDE
                updateStateNow()
            }
        }

        if (displayMode == DisplayMode.NOTIFICATION) {
            val notification = notificationInfo ?: return
            if (System.currentTimeMillis() - notification.startTime > notification.duration) {
                notificationInfo = null
                displayMode = lastDisplayMode
                targetState = when (displayMode) {
                    DisplayMode.MODULE -> IslandState.LARGE
                    DisplayMode.CLIENT -> IslandState.WIDE
                    else -> IslandState.WIDE
                }
                updateStateNow()
            }
        }

        if (displayMode == DisplayMode.MODULE_TOGGLE) {
            if (System.currentTimeMillis() - moduleToggleTime > moduleToggleDuration) {
                moduleToggled = false

                if (scaffoldActive) {
                    displayMode = DisplayMode.MODULE
                    targetState = IslandState.LARGE
                }
                else {
                    displayMode = DisplayMode.CLIENT
                    targetState = IslandState.WIDE
                }
                updateStateNow()
            }
        }

        if (currentState != targetState &&
            System.currentTimeMillis() - lastStateChange > stateChangeCooldown) {
            currentState = targetState
            lastStateChange = System.currentTimeMillis()
        }
    }

    private fun updateModuleTracking() {
        val allModules = BloomClient.moduleManager.modules

        for (module in allModules) {
            val moduleName = module.name
            val currentState = module.state
            val previousState = moduleStates[moduleName] ?: false

            moduleStates[moduleName] = currentState

            if (currentState != previousState) {
                moduleLastToggleTime[moduleName] = System.currentTimeMillis()

                if (moduleName == "Scaffold") {
                    handleScaffoldStateChange(currentState)
                }
                else if (!excludedFromNotification.contains(moduleName)) {
                    showModuleToggle(moduleName, currentState)
                }
            }
        }

        if (scaffoldActive && System.currentTimeMillis() - scaffoldLastUpdate > 500) {
            scaffoldLastUpdate = System.currentTimeMillis()

            if (moduleInfo?.name == "Scaffold") {
                moduleInfo = moduleInfo?.copy(
                    infoText = "${getBlockCount()} blocks left",
                    progressValue = getBlockProgress()
                )
            }
        }
    }

    private fun updateBlocksPerSecond() {
        val currentTime = System.currentTimeMillis()
        val currentBlockCount = getBlockCount()
        if (System.currentTimeMillis() - lastBlockCountTime > 1000) {
            val blockDifference = lastBlockCount - currentBlockCount
            val timeDifference = (currentTime - lastBlockCountTime) / 1000f
            blocksPerSecond = if (timeDifference > 0 && blockDifference > 0) {
                blockDifference / timeDifference
            } else {
                getPlayerSpeed().toFloat() * 0.3f
            }
            lastBlockCount = currentBlockCount
            lastBlockCountTime = currentTime
        }
    }

    private fun handleScaffoldStateChange(enabled: Boolean) {
        if (enabled && !scaffoldActive) {
            scaffoldActive = true
            scaffoldLastUpdate = System.currentTimeMillis()
            initialBlockCount = getBlockCount().coerceAtLeast(1)
            lastBlockCount = initialBlockCount
            lastBlockCountTime = System.currentTimeMillis()

            moduleInfo = ModuleInfo(
                name = "Scaffold",
                displayName = "Scaffold",
                infoText = "${getBlockCount()} blocks left",
                color = moduleAccentColor,
                showProgress = true,
                progressValue = getBlockProgress()
            )
        } else if (!enabled && scaffoldActive) {
            scaffoldActive = false
            initialBlockCount = 0
            blocksPerSecond = 0f
        }
    }

    private fun showModuleToggle(moduleName: String, enabled: Boolean) {
        moduleToggled = true
        moduleToggleTime = System.currentTimeMillis()

        moduleInfo = ModuleInfo(
            name = moduleName,
            displayName = moduleName,
            infoText = if (enabled) "$moduleName has been Enabled!" else "$moduleName has been Disabled!",
            color = if (enabled) Color(0, 184, 148) else Color(255, 82, 82),
            isEnabled = enabled
        )
    }

    private fun getCurrentStateWidthMultiplier(): Float {
        if (currentState == targetState) return currentState.widthMultiplier
        return currentState.widthMultiplier +
                (targetState.widthMultiplier - currentState.widthMultiplier) * stateProgress.toFloat()
    }

    private fun getCurrentStateHeightMultiplier(): Float {
        if (currentState == targetState) return currentState.heightMultiplier
        return currentState.heightMultiplier +
                (targetState.heightMultiplier - currentState.heightMultiplier) * stateProgress.toFloat()
    }

    private fun updateStateNow() {
        lastStateChange = 0
    }

    fun showNotification(
        title: String,
        message: String,
        icon: NotificationIcon = NotificationIcon.INFO,
        durationMs: Long = 3000L,
        showProgress: Boolean = false,
        progressValue: Float = 0f
    ) {
        notificationInfo = NotificationInfo(
            title = title,
            message = message,
            icon = icon,
            duration = durationMs,
            showProgress = showProgress,
            progressValue = progressValue
        )

        lastDisplayMode = displayMode
        displayMode = DisplayMode.NOTIFICATION
        targetState = IslandState.NOTIFICATION
        updateStateNow()
    }

    private fun getProgressValue(): Float {
        return when (displayMode) {
            DisplayMode.NOTIFICATION -> {
                if (notificationInfo?.showProgress == true) {
                    notificationInfo?.progressValue ?: 0f
                } else {
                    val notification = notificationInfo ?: return 0f
                    val timeElapsed = System.currentTimeMillis() - notification.startTime
                    val timeRemaining = notification.duration - timeElapsed
                    (timeRemaining.toFloat() / notification.duration.toFloat()).coerceIn(0f, 1f)
                }
            }
            DisplayMode.MODULE -> {
                if (moduleInfo?.name == "Scaffold") {
                    getBlockProgress()
                } else {
                    moduleInfo?.progressValue ?: 0f
                }
            }
            else -> 0f
        }
    }

    private fun getPlayerSpeed(): Double {
        val player = mc.thePlayer ?: return 0.0
        val xDiff = player.posX - player.prevPosX
        val zDiff = player.posZ - player.prevPosZ
        return sqrt(xDiff * xDiff + zDiff * zDiff) * 20
    }

    private fun getBlockCount(): Int {
        val player = mc.thePlayer ?: return 0
        return player.inventory.mainInventory.sumOf {
            if (it != null && it.isBlockStack()) it.stackSize else 0
        }
    }

    private fun net.minecraft.item.ItemStack?.isBlockStack(): Boolean {
        if (this == null) return false
        val item = this.item
        return item is net.minecraft.item.ItemBlock
    }

    private fun getBlockProgress(): Float {
        val currentCount = getBlockCount()

        if (initialBlockCount <= 0) {
            initialBlockCount = currentCount.coerceAtLeast(1)
        }

        return (currentCount.toFloat() / initialBlockCount.toFloat()).coerceIn(0f, 1f)
    }

    fun onClick(mouseX: Int, mouseY: Int, mouseButton: Int): Boolean {
        if (isHovered(mouseX, mouseY)) {
            targetState = if (currentState == IslandState.WIDE) {
                IslandState.LARGE
            } else {
                IslandState.WIDE
            }
            lastStateChange = System.currentTimeMillis()
            return true
        }
        return false
    }

    private fun isHovered(mouseX: Int, mouseY: Int): Boolean {
        val width = animWidth.value.toFloat()
        val height = animHeight.value.toFloat()

        val sr = ScaledResolution(mc)
        val screenWidth = sr.scaledWidth
        val screenHeight = sr.scaledHeight

        val xPos = when (side.horizontal) {
            Side.Horizontal.LEFT -> renderX.toFloat()
            Side.Horizontal.MIDDLE -> screenWidth / 2f - width / 2f
            Side.Horizontal.RIGHT -> screenWidth.toFloat() - renderX.toFloat() - width
        }

        val yPos = when (side.vertical) {
            Side.Vertical.TOP -> renderY.toFloat()
            Side.Vertical.MIDDLE -> screenHeight / 2f - height / 2f
            Side.Vertical.BOTTOM -> screenHeight.toFloat() - renderY.toFloat() - height
        }

        return mouseX >= xPos && mouseX <= xPos + width &&
                mouseY >= yPos && mouseY <= yPos + height
    }
}
