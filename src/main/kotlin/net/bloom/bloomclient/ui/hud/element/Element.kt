package net.bloom.bloomclient.ui.hud.element

import net.bloom.bloomclient.file.Configurable
import net.minecraft.client.gui.ScaledResolution
import net.minecraft.client.gui.Side
import kotlin.math.max
import kotlin.math.min

abstract class Element(
    val name: String,
    var x: Float = 2f,
    var y: Float = 2f,
    scale: Float = 1f,
    var side: Side = Side.DEFAULT
): Configurable() {
    val info = javaClass.getAnnotation(ElementInfo::class.java) ?: throw IllegalArgumentException("Missing element info.")

    var scale = 1f
        get() {
            if (info.disableScale)
                return 1f
            return field
        }
        set(value) {
            if (info.disableScale)
                return

            field = value
        }

    var renderX
        get() = when (side.horizontal) {
            Side.Horizontal.LEFT -> x
            Side.Horizontal.MIDDLE -> (ScaledResolution(mc).scaledWidth / 2) - x
            Side.Horizontal.RIGHT -> ScaledResolution(mc).scaledWidth - x
        }
        set(value) = when (side.horizontal) {
            Side.Horizontal.LEFT -> {
                x += value
            }

            Side.Horizontal.MIDDLE, Side.Horizontal.RIGHT -> {
                x -= value
            }
        }

    var renderY
        get() = when (side.vertical) {
            Side.Vertical.TOP -> y
            Side.Vertical.MIDDLE -> (ScaledResolution(mc).scaledHeight / 2) - y
            Side.Vertical.BOTTOM -> ScaledResolution(mc).scaledHeight - y
        }
        set(value) = when (side.vertical) {
            Side.Vertical.TOP -> {
                y += value
            }

            Side.Vertical.MIDDLE, Side.Vertical.BOTTOM -> {
                y -= value
            }
        }

    init {
        this.scale = scale
    }

    var border: Border? = null

    var drag = false
    var prevMouseX = 0F
    var prevMouseY = 0F

    /**
     * Called when element created
     */
    open fun createElement() = true

    /**
     * Called when element destroyed
     */
    open fun destroyElement() {}

    /**
     * Draw element
     */
    abstract fun drawElement(): Border?

    /**
     * Update element
     */
    open fun updateElement() {}

    /**
     * Check if [x] and [y] is in element border
     */
    open fun isInBorder(x: Float, y: Float): Boolean {
        val border = border ?: return false

        val minX = min(border.x, border.x2)
        val minY = min(border.y, border.y2)

        val maxX = max(border.x, border.x2)
        val maxY = max(border.y, border.y2)

        return minX <= x && minY <= y && maxX >= x && maxY >= y
    }

    /**
     * Called when mouse clicked
     */
    open fun handleMouseClick(x: Float, y: Float, mouseButton: Int) {}

    /**
     * Called when key pressed
     */
    open fun handleKey(c: Char, keyCode: Int) {}
}