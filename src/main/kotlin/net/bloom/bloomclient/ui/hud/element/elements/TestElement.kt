package net.bloom.bloomclient.ui.hud.element.elements

import net.bloom.bloomclient.ui.hud.element.Border
import net.bloom.bloomclient.ui.hud.element.Element
import net.bloom.bloomclient.ui.hud.element.ElementInfo
import net.minecraft.client.gui.Side

@ElementInfo(name = "Test")
class TestElement(
    x: Float = -8F,
    y: Float = 57F,
    scale: Float = 1F,
    side: Side = Side(Side.Horizontal.MIDDLE, Side.Vertical.BOTTOM)
): Element("Test", x, y, scale, side) {
    private var floatValue = float("FloatValue", 0f, 0f, 1f)
    private var boolValue = bool("BoolValue", true)
    private var listValue = list("ListValue", "1", arrayOf("1", "2", "3", "abc", "99999999999"))

    override fun drawElement() = Border(0f, 0f, 0f, 0f)

}